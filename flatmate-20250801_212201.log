2025-08-01 21:22:01 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-08-01 21:22:03 - [fm.core.services.folder_monitor_service] [INFO] - FolderMonitorService initialized
2025-08-01 21:22:03 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-01 21:22:03 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-08-01 21:22:03 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-08-01 21:22:04 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded 0 component defaults for categorize from C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\config\defaults.yaml
2025-08-01 21:22:04 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 0 user preferences
2025-08-01 21:22:04 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-08-01 21:22:04 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-08-01 21:22:04 - [main] [INFO] - Application starting...
2025-08-01 21:22:07 - [main] [INFO] - 
=== Initializing Database & Cache ===
2025-08-01 21:22:07 - [fm.core.data_services.db_io_service] [INFO] - Initializing DBIOService singleton...
2025-08-01 21:22:07 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-08-01 21:22:07 - [fm.core.database.sql_repository.cached_sqlite_repository] [DEBUG] - CachedSQLiteRepository initialized
2025-08-01 21:22:07 - [fm.core.database.sql_repository.cached_sqlite_repository] [INFO] - Warming transaction cache...
2025-08-01 21:22:08 - [fm.core.database.sql_repository.cached_sqlite_repository] [INFO] - Cache warmed successfully: 2166 transactions, 3 unique accounts in 0.95s
2025-08-01 21:22:08 - [fm.core.data_services.db_io_service] [INFO] - DBIOService initialized with cache: 2166 transactions
2025-08-01 21:22:08 - [main] [INFO] - 
=== Initializing Auto-Import Manager ===
2025-08-01 21:22:08 - [fm.core.services.folder_monitor_service] [INFO] - Folder monitor worker thread started
2025-08-01 21:22:08 - [fm.core.services.folder_monitor_service] [INFO] - Folder monitor service started
2025-08-01 21:22:08 - [main] [INFO] - 
=== Setting up Module Coordinator ===
2025-08-01 21:22:08 - [fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-08-01 21:22:08 - [fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-08-01 21:22:08 - [fm.module_coordinator] [INFO] - Creating all modules (eager loading)
2025-08-01 21:22:08 - [fm.modules.base.base_presenter] [DEBUG] - Initialized HomePresenter
2025-08-01 21:22:08 - [fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-08-01 21:22:08 - [fm.modules.base.base_presenter] [DEBUG] - Initialized UpdateDataPresenter
2025-08-01 21:22:08 - [fm.modules.update_data.ud_presenter] [DEBUG] - log level set to debug in ud_presenter.py
2025-08-01 21:22:08 - [fm.modules.update_data.ud_presenter] [DEBUG] - UPDATE_DATA: Debug logging enabled for console output
2025-08-01 21:22:08 - [fm.modules.base.base_presenter] [DEBUG] - Initialized CategorizePresenter
2025-08-01 21:22:08 - [fm.module_coordinator] [INFO] - Setting up home module
2025-08-01 21:22:08 - [fm.modules.base.base_presenter] [INFO] - Setting up HomePresenter
2025-08-01 21:22:08 - [fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-08-01 21:22:08 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter setup complete
2025-08-01 21:22:08 - [fm.module_coordinator] [INFO] - Setting up update_data module
2025-08-01 21:22:08 - [fm.modules.base.base_presenter] [INFO] - Setting up UpdateDataPresenter
2025-08-01 21:22:09 - [fm.modules.update_data._presenter.file_list_manager] [DEBUG] - [FILE_LIST_MANAGER] Initialized with empty file list
2025-08-01 21:22:09 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - [FILE_MANAGER] Initialized with FileListManager delegation
2025-08-01 21:22:09 - [fm.core.services.folder_monitor_service] [DEBUG] - Registered file discovery callback: FileListManager._on_files_discovered
2025-08-01 21:22:09 - [fm.modules.update_data.ud_presenter] [DEBUG] - Connected file_pane.publish_file_removed to FileListManager
2025-08-01 21:22:09 - [fm.modules.update_data.ud_presenter] [DEBUG] - Connected file_pane.publish_files_added to FileListManager
2025-08-01 21:22:09 - [fm.modules.update_data.ud_presenter] [DEBUG] - Connected file_pane.publish_add_files_requested
2025-08-01 21:22:09 - [fm.modules.update_data.ud_presenter] [DEBUG] - Connected file pane folder monitoring toggle signal to FileListManager
2025-08-01 21:22:09 - [fm.modules.update_data.ud_presenter] [DEBUG] - Connected guide pane folder monitoring toggle signal to FileListManager
2025-08-01 21:22:09 - [fm.modules.update_data._view.center_panel_components.file_pane] [ERROR] - [FILE_PANE] Error connecting to file list events: No module named 'fm.modules.update_data._view.services'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_view\center_panel_components\file_pane.py", line 182, in connect_to_file_list_manager
    from ..services.local_event_bus import ViewEvents
ModuleNotFoundError: No module named 'fm.modules.update_data._view.services'
2025-08-01 21:22:09 - [fm.modules.update_data.ud_view] [DEBUG] - [UD_VIEW] Connected file pane to FileListManager events
2025-08-01 21:22:09 - [fm.modules.update_data.ud_presenter] [DEBUG] - Signals connected and event bridges set up
2025-08-01 21:22:09 - [fm.modules.update_data.ud_presenter] [DEBUG] - Added FILE_DISPLAY_UPDATED event subscription
2025-08-01 21:22:09 - [fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter setup complete
2025-08-01 21:22:09 - [fm.module_coordinator] [INFO] - Setting up categorize module
2025-08-01 21:22:09 - [fm.modules.base.base_presenter] [INFO] - Setting up CategorizePresenter
2025-08-01 21:22:09 - [fm.core.database.sql_repository.cached_sqlite_repository] [DEBUG] - Cache hit: returning 3 unique accounts
2025-08-01 21:22:09 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Initializing TransactionViewPanel
2025-08-01 21:22:09 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: TransactionViewPanel._init_ui
2025-08-01 21:22:09 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting up TransactionViewPanel UI
2025-08-01 21:22:09 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Creating CustomTableView_v2 for transactions
2025-08-01 21:22:09 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel UI setup complete
2025-08-01 21:22:09 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: TransactionViewPanel._init_ui took 0.124s
2025-08-01 21:22:09 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Connecting TransactionViewPanel signals
2025-08-01 21:22:09 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel signals connected
2025-08-01 21:22:09 - [fm.modules.categorize.cat_presenter] [DEBUG] - About to call _load_data_during_setup()
2025-08-01 21:22:09 - [fm.modules.categorize.cat_presenter] [DEBUG] - Loading data during setup (eager loading)
2025-08-01 21:22:10 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.last_account = None (Source: cat_presenter.py:_load_data_during_setup)
2025-08-01 21:22:10 - [fm.modules.categorize.cat_presenter] [INFO] - Auto-loading ALL transactions from database...
2025-08-01 21:22:10 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: CategorizePresenter._handle_load_db
2025-08-01 21:22:10 - [fm.modules.categorize.cat_presenter] [INFO] - Loading transactions from database for categorisation…
2025-08-01 21:22:10 - [fm.modules.categorize.cat_presenter] [DEBUG] - Filters: None
2025-08-01 21:22:10 - [fm.modules.categorize.cat_presenter] [DEBUG] - Fetching transactions with filters: {}
2025-08-01 21:22:10 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Data Retrieval from Cache
2025-08-01 21:22:10 - [fm.modules.categorize.cat_presenter] [INFO] - Retrieved 2166 transactions as DataFrame
2025-08-01 21:22:10 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Data Retrieval from Cache took 0.135s
2025-08-01 21:22:10 - [fm.modules.categorize.cat_presenter] [DEBUG] - DataFrame shape: (2166, 32), empty: False
2025-08-01 21:22:10 - [fm.modules.categorize.cat_presenter] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id', 'id', 'import_date', 'modified_date', 'is_deleted']
2025-08-01 21:22:10 - [fm.modules.categorize.cat_presenter] [DEBUG] - First few rows:
              account  ...  is_deleted
0  38-9004-0646977-04  ...           0
1  38-9004-0646977-04  ...           0
2  38-9004-0646977-04  ...           0
3  38-9004-0646977-04  ...           0
4  38-9004-0646977-00  ...           0

[5 rows x 32 columns]
2025-08-01 21:22:10 - [fm.modules.categorize.cat_presenter] [DEBUG] - Using DataFrame with shape: (2166, 32)
2025-08-01 21:22:10 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Transaction Categorization
2025-08-01 21:22:10 - [fm.modules.categorize.cat_presenter] [DEBUG] - Applying categorization to transactions...
2025-08-01 21:22:10 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Transaction Categorization took 0.127s
2025-08-01 21:22:10 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: CategorizePresenter._apply_default_sorting
2025-08-01 21:22:10 - [fm.modules.categorize.cat_presenter] [DEBUG] - Applied default sorting: date (descending)
2025-08-01 21:22:10 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: CategorizePresenter._apply_default_sorting took 0.016s
2025-08-01 21:22:10 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Table View Data Setting
2025-08-01 21:22:10 - [fm.modules.categorize.cat_presenter] [DEBUG] - Setting DataFrame with 2166 transactions to view
2025-08-01 21:22:10 - [fm.modules.categorize._view.cat_view] [DEBUG] - CatView setting dataframe: 2166 rows
2025-08-01 21:22:10 - [fm.modules.categorize._view.cat_view] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id', 'id', 'import_date', 'modified_date', 'is_deleted']
2025-08-01 21:22:10 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting transactions: 2166 rows
2025-08-01 21:22:10 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Using ordered display columns: ['date', 'details', 'amount', 'account', 'balance', 'category', 'tags', 'notes']
2025-08-01 21:22:10 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Reordered DataFrame columns: ['date', 'details', 'amount', 'account', 'balance', 'category', 'tags', 'notes', 'credit_amount', 'db_uid', 'debit_amount', 'empty', 'hash', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tp_code', 'tp_part', 'tp_ref', 'unique_id', 'id', 'import_date', 'modified_date', 'is_deleted']
2025-08-01 21:22:18 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Displaying columns: ['Date', 'Details', 'Amount', 'Account', 'Balance', 'Category', 'Tags', 'Notes']
2025-08-01 21:22:18 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Table View Data Setting took 7.798s
2025-08-01 21:22:18 - [fm.modules.categorize.cat_presenter] [INFO] - Successfully loaded and displayed 2166 transactions in 8.6s (252.8 txns/s)
2025-08-01 21:22:18 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: CategorizePresenter._handle_load_db took 8.618s
2025-08-01 21:22:18 - [fm.modules.categorize.cat_presenter] [DEBUG] - Data loading during setup complete
2025-08-01 21:22:18 - [fm.modules.base.base_presenter] [DEBUG] - CategorizePresenter setup complete
2025-08-01 21:22:18 - [fm.module_coordinator] [INFO] - All modules created and configured
2025-08-01 21:22:18 - [fm.module_coordinator] [DEBUG] - Available modules: ['home', 'update_data', 'categorize']
2025-08-01 21:22:18 - [fm.module_coordinator] [INFO] - Starting Application
2025-08-01 21:22:18 - [fm.module_coordinator] [INFO] - Transitioning from None to home
2025-08-01 21:22:18 - [fm.module_coordinator] [DEBUG] - Showing home module
2025-08-01 21:22:18 - [fm.modules.base.base_presenter] [INFO] - Showing HomePresenter
2025-08-01 21:22:18 - [fm.modules.base.base_module_view] [INFO] - Setting up HomeView in Main Window
2025-08-01 21:22:18 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-08-01 21:22:18 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-08-01 21:22:18 - [fm.modules.base.base_module_view] [INFO] - HomeView setup complete
2025-08-01 21:22:18 - [fm.modules.home.home_presenter] [DEBUG] - Refreshing Home content
2025-08-01 21:22:18 - [fm.modules.home.home_presenter] [DEBUG] - Home content refresh complete
2025-08-01 21:22:18 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now visible
2025-08-01 21:22:18 - [fm.module_coordinator] [INFO] - Successfully transitioned to home
2025-08-01 21:22:18 - [main] [INFO] - 
=== Checking Auto-Import Status ===
2025-08-01 21:22:19 - [main] [INFO] - Auto-import startup check temporarily disabled - using new state management
2025-08-01 21:22:19 - [main] [INFO] - 
=== Application Ready ===
2025-08-01 21:22:21 - [fm.module_coordinator] [INFO] - Transitioning from HomePresenter to update_data
2025-08-01 21:22:21 - [fm.module_coordinator] [DEBUG] - Hiding HomePresenter
2025-08-01 21:22:21 - [fm.modules.base.base_presenter] [INFO] - Hiding HomePresenter
2025-08-01 21:22:21 - [fm.modules.base.base_module_view] [DEBUG] - Cleaning up HomeView from main window
2025-08-01 21:22:21 - [fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-08-01 21:22:21 - [fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-08-01 21:22:21 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now hidden
2025-08-01 21:22:21 - [fm.module_coordinator] [DEBUG] - Showing update_data module
2025-08-01 21:22:21 - [fm.modules.base.base_presenter] [INFO] - Showing UpdateDataPresenter
2025-08-01 21:22:21 - [fm.modules.base.base_module_view] [INFO] - Setting up UpdateDataView in Main Window
2025-08-01 21:22:21 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-08-01 21:22:21 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-08-01 21:22:21 - [fm.modules.base.base_module_view] [INFO] - UpdateDataView setup complete
2025-08-01 21:22:21 - [fm.modules.update_data.ud_presenter] [DEBUG] - Refreshing UpdateData content
2025-08-01 21:22:21 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - Source option changed to: Select individual files...
2025-08-01 21:22:21 - [fm.modules.update_data.ud_presenter] [DEBUG] - UpdateData content refresh complete
2025-08-01 21:22:21 - [fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter is now visible
2025-08-01 21:22:21 - [fm.module_coordinator] [INFO] - Successfully transitioned to update_data
2025-08-01 21:22:24 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - Source selection requested: Select individual files...
2025-08-01 21:22:24 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - [FILE_MANAGER] Opening file dialog with last_dir: C:/Users/<USER>/Downloads/_flatmete_file_moniter_test
2025-08-01 21:22:33 - [fm.modules.update_data._presenter.file_list_manager] [DEBUG] - [FILE_LIST_MANAGER] Set file list: 6 files
2025-08-01 21:22:33 - [fm.modules.update_data._presenter.file_list_manager] [DEBUG] - [FILE_LIST_MANAGER] Emitted FILE_LIST_UPDATED event (6 files)
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 02-1245-0452229-002_01Aug2023_15Dec2024_co-op_bank.Csv
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-01 21:22:33 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '02-1245-0452229-002_01Aug2023_15Dec2024_co-op_bank.Csv': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '02-1245-0452229-002_01Aug2023_15Dec2024_co-op_bank.Csv': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankFullCSVHandler did not match
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: CoopStandardCSVHandler
2025-08-01 21:22:33 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler CoopStandardCSVHandler score for '02-1245-0452229-002_01Aug2023_15Dec2024_co-op_bank.Csv': 20 (Account + Columns)
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: CoopStandardCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-00_01Oct_kbank_basic.CSV
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-01 21:22:33 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-00_01Oct_kbank_basic.CSV': 15 (Account: True, Cols: False, NumCols: True) -> Pass
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankBasicCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-00_13Jun.CSV
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-01 21:22:33 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-00_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-00_13Jun.CSV': 20 (Account + Columns)
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-00_17Oct_kbank_fullCSV.CSV
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-01 21:22:33 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-00_17Oct_kbank_fullCSV.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-00_17Oct_kbank_fullCSV.CSV': 20 (Account + Columns)
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-04_13Jun.CSV
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-01 21:22:33 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-04_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-04_13Jun.CSV': 20 (Account + Columns)
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: fmMaster_20250731_002307.csv
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-01 21:22:33 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for 'fmMaster_20250731_002307.csv': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for 'fmMaster_20250731_002307.csv': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankFullCSVHandler did not match
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: CoopStandardCSVHandler
2025-08-01 21:22:33 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler CoopStandardCSVHandler score for 'fmMaster_20250731_002307.csv': 10 (Account: True, Cols: True, NumCols: False) -> Fail
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler CoopStandardCSVHandler did not match
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: ASBStandardCSVHandler
2025-08-01 21:22:33 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers.asb_standard_csv_handler] [WARNING] - Could not extract full ASB account number from metadata line: nan
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler ASBStandardCSVHandler score for 'fmMaster_20250731_002307.csv': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler ASBStandardCSVHandler did not match
2025-08-01 21:22:33 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] No matching handler found for file: fmMaster_20250731_002307.csv
2025-08-01 21:22:33 - [fm.modules.update_data.ud_view] [DEBUG] - Displaying enriched file info: [{'path': 'C:\\Users\\<USER>\\Downloads\\_flatmete_file_moniter_test\\02-1245-0452229-002_01Aug2023_15Dec2024_co-op_bank.Csv', 'size_bytes': 3440, 'size_str': '3.4 KB', 'bank_type': 'Co-operative Bank', 'format_type': 'standard', 'handler': 'CoopStandardCSVHandler'}, {'path': 'C:\\Users\\<USER>\\Downloads\\_flatmete_file_moniter_test\\38-9004-0646977-00_01Oct_kbank_basic.CSV', 'size_bytes': 3359, 'size_str': '3.3 KB', 'bank_type': 'Kiwibank', 'format_type': 'basic', 'handler': 'KiwibankBasicCSVHandler'}, {'path': 'C:\\Users\\<USER>\\Downloads\\_flatmete_file_moniter_test\\38-9004-0646977-00_13Jun.CSV', 'size_bytes': 121907, 'size_str': '119.0 KB', 'bank_type': 'Kiwibank', 'format_type': 'full', 'handler': 'KiwibankFullCSVHandler'}, {'path': 'C:\\Users\\<USER>\\Downloads\\_flatmete_file_moniter_test\\38-9004-0646977-00_17Oct_kbank_fullCSV.CSV', 'size_bytes': 6578, 'size_str': '6.4 KB', 'bank_type': 'Kiwibank', 'format_type': 'full', 'handler': 'KiwibankFullCSVHandler'}, {'path': 'C:\\Users\\<USER>\\Downloads\\_flatmete_file_moniter_test\\38-9004-0646977-04_13Jun.CSV', 'size_bytes': 149541, 'size_str': '146.0 KB', 'bank_type': 'Kiwibank', 'format_type': 'full', 'handler': 'KiwibankFullCSVHandler'}, {'path': 'C:\\Users\\<USER>\\Downloads\\_flatmete_file_moniter_test\\fmMaster_20250731_002307.csv', 'size_bytes': 342833, 'size_str': '334.8 KB', 'bank_type': 'Unknown', 'format_type': 'Unrecognized', 'handler': None}]
2025-08-01 21:22:33 - [fm.modules.update_data._presenter.file_manager] [ERROR] - Error selecting files: 'FileBrowser' object has no attribute 'set_enriched_files'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_presenter\file_manager.py", line 127, in _select_files
    self.view.display_enriched_file_info(enriched_info)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\ud_view.py", line 285, in display_enriched_file_info
    self.center_display.display_enriched_file_info(file_info_list)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_view\center_panel.py", line 122, in display_enriched_file_info
    self.file_pane.display_enriched_file_info(file_info_list)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_view\center_panel_components\file_pane.py", line 260, in display_enriched_file_info
    self.file_browser.set_enriched_files(file_info_list, source_dir)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'FileBrowser' object has no attribute 'set_enriched_files'
2025-08-01 21:22:33 - [fm.modules.update_data._presenter.file_manager] [ERROR] - [FILE_MANAGER] Error selecting files: 'FileBrowser' object has no attribute 'set_enriched_files'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_presenter\file_manager.py", line 127, in _select_files
    self.view.display_enriched_file_info(enriched_info)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\ud_view.py", line 285, in display_enriched_file_info
    self.center_display.display_enriched_file_info(file_info_list)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_view\center_panel.py", line 122, in display_enriched_file_info
    self.file_pane.display_enriched_file_info(file_info_list)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_view\center_panel_components\file_pane.py", line 260, in display_enriched_file_info
    self.file_browser.set_enriched_files(file_info_list, source_dir)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'FileBrowser' object has no attribute 'set_enriched_files'
2025-08-01 21:22:38 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - Source selection requested: Select individual files...
2025-08-01 21:22:38 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - [FILE_MANAGER] Opening file dialog with last_dir: C:/Users/<USER>/Downloads/_flatmete_file_moniter_test
2025-08-01 21:22:46 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - Source option changed to: Select entire folder...
2025-08-01 21:22:49 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - Source selection requested: Select entire folder...
2025-08-01 21:22:49 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - [FILE_MANAGER] Opening folder dialog with last_dir: C:/Users/<USER>/Downloads/_flatmete_file_moniter_test
2025-08-01 21:22:53 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - [FILE_MANAGER] Dialog returned folder_path: C:/Users/<USER>/Downloads/_flatmete_file_moniter_test
2025-08-01 21:22:53 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - [FILE_MANAGER] Processing selected folder: C:/Users/<USER>/Downloads/_flatmete_file_moniter_test
2025-08-01 21:22:53 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - [FILE_MANAGER] Saved last_dir to config: C:/Users/<USER>/Downloads/_flatmete_file_moniter_test
2025-08-01 21:22:53 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - [FILE_MANAGER] Discovering files in folder...
2025-08-01 21:22:53 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - Discovered 6 CSV files in C:/Users/<USER>/Downloads/_flatmete_file_moniter_test
2025-08-01 21:22:53 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - [FILE_MANAGER] Discovered 6 files: ['C:\\Users\\<USER>\\Downloads\\_flatmete_file_moniter_test\\02-1245-0452229-002_01Aug2023_15Dec2024_co-op_bank.Csv', 'C:\\Users\\<USER>\\Downloads\\_flatmete_file_moniter_test\\38-9004-0646977-00_01Oct_kbank_basic.CSV', 'C:\\Users\\<USER>\\Downloads\\_flatmete_file_moniter_test\\38-9004-0646977-00_13Jun.CSV', 'C:\\Users\\<USER>\\Downloads\\_flatmete_file_moniter_test\\38-9004-0646977-00_17Oct_kbank_fullCSV.CSV', 'C:\\Users\\<USER>\\Downloads\\_flatmete_file_moniter_test\\38-9004-0646977-04_13Jun.CSV', 'C:\\Users\\<USER>\\Downloads\\_flatmete_file_moniter_test\\fmMaster_20250731_002307.csv']
2025-08-01 21:22:53 - [fm.modules.update_data._presenter.file_list_manager] [DEBUG] - [FILE_LIST_MANAGER] Set file list: 6 files
2025-08-01 21:22:53 - [fm.modules.update_data._presenter.file_list_manager] [DEBUG] - [FILE_LIST_MANAGER] Updated folder info: C:\Users\<USER>\Downloads\_flatmete_file_moniter_test (6 files)
2025-08-01 21:22:53 - [fm.modules.update_data._presenter.file_list_manager] [DEBUG] - [FILE_LIST_MANAGER] Emitted FILE_LIST_UPDATED event (6 files)
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 02-1245-0452229-002_01Aug2023_15Dec2024_co-op_bank.Csv
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-01 21:22:53 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '02-1245-0452229-002_01Aug2023_15Dec2024_co-op_bank.Csv': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '02-1245-0452229-002_01Aug2023_15Dec2024_co-op_bank.Csv': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankFullCSVHandler did not match
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: CoopStandardCSVHandler
2025-08-01 21:22:53 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler CoopStandardCSVHandler score for '02-1245-0452229-002_01Aug2023_15Dec2024_co-op_bank.Csv': 20 (Account + Columns)
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: CoopStandardCSVHandler
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-00_01Oct_kbank_basic.CSV
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-01 21:22:53 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-00_01Oct_kbank_basic.CSV': 15 (Account: True, Cols: False, NumCols: True) -> Pass
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankBasicCSVHandler
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-00_13Jun.CSV
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-01 21:22:53 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-00_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-00_13Jun.CSV': 20 (Account + Columns)
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-00_17Oct_kbank_fullCSV.CSV
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-01 21:22:53 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-00_17Oct_kbank_fullCSV.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-00_17Oct_kbank_fullCSV.CSV': 20 (Account + Columns)
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-04_13Jun.CSV
2025-08-01 21:22:53 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-01 21:22:53 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-04_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-04_13Jun.CSV': 20 (Account + Columns)
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: fmMaster_20250731_002307.csv
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-01 21:22:54 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for 'fmMaster_20250731_002307.csv': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for 'fmMaster_20250731_002307.csv': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankFullCSVHandler did not match
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: CoopStandardCSVHandler
2025-08-01 21:22:54 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler CoopStandardCSVHandler score for 'fmMaster_20250731_002307.csv': 10 (Account: True, Cols: True, NumCols: False) -> Fail
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler CoopStandardCSVHandler did not match
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: ASBStandardCSVHandler
2025-08-01 21:22:54 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers.asb_standard_csv_handler] [WARNING] - Could not extract full ASB account number from metadata line: nan
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler ASBStandardCSVHandler score for 'fmMaster_20250731_002307.csv': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler ASBStandardCSVHandler did not match
2025-08-01 21:22:54 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] No matching handler found for file: fmMaster_20250731_002307.csv
2025-08-01 21:22:54 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - [FILE_MANAGER] State updated - source_type: folder, can_process: False
2025-08-01 21:22:54 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - [FILE_MANAGER] Emitting SOURCE_DISCOVERED event
2025-08-01 21:22:54 - [fm.modules.update_data._presenter.file_manager] [DEBUG] - [FILE_MANAGER] SOURCE_DISCOVERED event emitted successfully
2025-08-01 21:22:54 - [fm.modules.update_data._view.center_panel_components.guide_pane] [DEBUG] - Added monitor folder checkbox to guide pane, enabled=False
