#!/usr/bin/env python3
"""
Event dataclasses for Update Data module.

Provides type-safe event data structures for the update data module's event system.
Replaces the dictionary-based EventDataFactory with proper dataclasses.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional
from datetime import datetime


def _now_iso() -> str:
    """Return current time in ISO format for default field values."""
    return datetime.now().isoformat()


@dataclass
class SourceSelectRequestEvent:
    """Event data for source selection requests."""
    selection_type: str
    path: Optional[str] = None
    timestamp: str = field(default_factory=_now_iso)


@dataclass
class SourceDiscoveredEvent:
    """Event data for source discovery."""
    source_type: str
    files: List[str]
    path: str
    count: int
    timestamp: str = field(default_factory=_now_iso)


@dataclass
class DestinationConfiguredEvent:
    """Event data for destination configuration."""
    destination_type: str  # 'same_as_source' or 'custom'
    path: str
    timestamp: str = field(default_factory=_now_iso)


@dataclass
class UiStateUpdateEvent:
    """Event data for UI state updates."""
    can_process: bool
    status_message: str
    timestamp: str = field(default_factory=_now_iso)
    # Additional fields can be added as needed
    extra_data: Dict = field(default_factory=dict)


@dataclass
class BusinessErrorEvent:
    """Event data for business errors."""
    error_type: str
    message: str
    context: Dict = field(default_factory=dict)
    timestamp: str = field(default_factory=_now_iso)


@dataclass
class DialogRequestEvent:
    """Event data for dialog requests."""
    dialog_type: str
    title: str
    timestamp: str = field(default_factory=_now_iso)
    # Additional fields can be added as needed
    extra_data: Dict = field(default_factory=dict)


# File display events

@dataclass
class FileDisplayUpdateEvent:
    """Event data for file display updates."""
    files: List[str]
    source_path: str
    timestamp: str = field(default_factory=_now_iso)


# Processing events

@dataclass
class ProcessingStartedEvent:
    """Event data for processing started."""
    job_sheet: Dict
    timestamp: str = field(default_factory=_now_iso)


@dataclass
class ProcessingCompletedEvent:
    """Event data for processing completed."""
    success: bool
    result: Dict = field(default_factory=dict)
    timestamp: str = field(default_factory=_now_iso)


# Dialog result events

@dataclass
class FilesDialogCompletedEvent:
    """Event data for file dialog completion."""
    selected_files: List[str]
    cancelled: bool = False
    timestamp: str = field(default_factory=_now_iso)


@dataclass
class FolderDialogCompletedEvent:
    """Event data for folder dialog completion."""
    selected_folder: Optional[str] = None
    cancelled: bool = False
    timestamp: str = field(default_factory=_now_iso)


# File list management events

@dataclass
class FileListUpdatedEvent:
    """Event data for file list updates."""
    files: List[str]
    source_path: str = ""
    timestamp: str = field(default_factory=_now_iso)


@dataclass
class FileAddedEvent:
    """Event data for individual file addition."""
    file_path: str
    timestamp: str = field(default_factory=_now_iso)


@dataclass
class FileRemovedEvent:
    """Event data for individual file removal."""
    file_path: str
    timestamp: str = field(default_factory=_now_iso)


@dataclass
class FolderMonitoringToggledEvent:
    """Event data for folder monitoring changes."""
    folder_path: str
    enabled: bool
    timestamp: str = field(default_factory=_now_iso)


@dataclass
class FilesDiscoveredEvent:
    """Event data for files discovered via monitoring."""
    files: List[str]
    source_folder: str
    timestamp: str = field(default_factory=_now_iso)
